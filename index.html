<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股债利差监控面板</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css"
        integrity="sha384-n8MVd4RsNIU0KOVEMVIARBEGvrrsueSGUofrtVOKugKGyZfuKXt8QRHQGCkCQl2z" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"
        integrity="sha384-XjKyOOlGwcjNTAIQHIpgOno0Hl1YQqzUOEleOLALmuqehneUG+vnGctmUb0ZY0l8"
        crossorigin="anonymous"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
        }

        .main-container {
            width: 100%;
            max-width: 1400px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #212529;
        }

        .update-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        #update-btn {
            padding: 8px 16px;
            font-size: 14px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        #update-btn:hover:not(:disabled) {
            background-color: #0056b3;
        }

        #update-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        #update-status {
            font-size: 14px;
            color: #6c757d;
        }

        /* Tab 样式 */
        .tab-container {
            display: flex;
            gap: 10px;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            background-color: transparent;
            border-bottom: 3px solid transparent;
            transition: color 0.3s, border-color 0.3s;
        }

        .tab-btn:hover {
            color: #0056b3;
        }

        .tab-btn.active {
            color: #007bff;
            border-bottom-color: #007bff;
            font-weight: 600;
        }

        /* 内容容器 */
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            width: 100%;
        }

        .info-card {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        .info-card h2 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            color: #495057;
            border-bottom: 1px solid #f1f3f5;
            padding-bottom: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            padding: 8px 0;
        }

        .info-item .label {
            color: #6c757d;
        }

        .info-item .value {
            font-weight: 600;
            color: #212529;
        }

        #current-signal-value {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
        }

        /* 公式和说明样式 */
        .formula-container {
            font-size: 1.1em;
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .explanation {
            font-size: 14px;
            color: #495057;
            line-height: 1.6;
        }

        .chart-container {
            width: 100%;
            height: 500px;
            background-color: #ffffff;
            border-radius: 12px;
            padding: 20px;
            box-sizing: border-box;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        /* 加载动画 */
        .loader-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            flex-direction: column;
            gap: 20px;
        }

        .spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>

    <div id="loader" class="loader-overlay">
        <div class="spinner"></div>
        <p>正在加载初始数据，请稍候...</p>
    </div>

    <div class="main-container">
        <div class="header">
            <h1>股债利差监控面板</h1>
            <div class="update-container">
                <span id="update-status"></span>
                <button id="update-btn">更新数据</button>
            </div>
        </div>

        <div class="tab-container">
            <button class="tab-btn active" data-index="csi300">沪深300</button>
            <button class="tab-btn" data-index="sse50">上证50</button>
        </div>

        <div class="content-grid">
            <div class="info-card">
                <h2>核心指标 (<span id="latest-date">--</span>)</h2>
                <div class="info-item">
                    <span class="label">当前股债利差 (%):</span>
                    <span class="value" id="current-yield-spread">--</span>
                </div>
                <div class="info-item">
                    <span class="label">当前历史分位数:</span>
                    <span class="value" id="current-percentile">--</span>
                </div>
                <hr style="border: none; border-top: 1px solid #f1f3f5; margin: 10px 0;">
                <div class="info-item">
                    <span class="label">五年内最大值 (%):</span>
                    <span class="value" id="5y-max">--</span>
                </div>
                <div class="info-item">
                    <span class="label">80% 分位值 (%):</span>
                    <span class="value" id="80-percentile">--</span>
                </div>
                <div class="info-item">
                    <span class="label">20% 分位值 (%):</span>
                    <span class="value" id="20-percentile">--</span>
                </div>
                <div class="info-item">
                    <span class="label">五年内最小值 (%):</span>
                    <span class="value" id="5y-min">--</span>
                </div>
            </div>

            <div class="info-card">
                <h2>策略信号</h2>
                <div class="info-item">
                    <span class="label">信号值:</span>
                    <span class="value" id="current-signal-value">--</span>
                </div>

                <h3>信号计算公式</h3>
                <div class="formula-container" id="formula-display">
                </div>

                <h3>买卖逻辑说明</h3>
                <p class="explanation">
                    该策略基于股债利差的历史分位数生成交易信号。
                    <br>• 当股债利差的<strong>历史分位数较高</strong>时 (例如 > 88%)，意味着股票相对于债券极具吸引力，策略会生成<strong>买入信号</strong>。
                    <br>• 当股债利差的<strong>历史分位数较低</strong>时 (例如 < 24%)，意味着股票相对于债券估值偏高，策略会生成<strong>卖出信号</strong>。
                        <br>• 在中间区域则<strong>持有</strong>。
                        <br>信号值为正数代表买入比例，负数代表卖出比例。
                </p>
            </div>
        </div>

        <div id="main-chart" class="chart-container"></div>
        <div id="signal-chart" class="chart-container"></div>
    </div>

    <script>
        /**
         * Sigmoid策略函数 (参数与原版保持一致)
         */
        function sigmoidStrategy(percentile, params = {}) {
            const {
                sell_center = 0.2385, buy_center = 0.8814,
                sell_ratio = 0.0795, buy_ratio = 0.0295,
                steepness = 28.4524
            } = params;
            const sell_signal = 1 / (1 + Math.exp(steepness * (percentile - sell_center)));
            const buy_signal = 1 / (1 + Math.exp(-steepness * (percentile - buy_center)));

            if (sell_signal > 0.5) return -sell_ratio * sell_signal;
            if (buy_signal > 0.5) return buy_ratio * buy_signal;
            return 0;
        }

        document.addEventListener('DOMContentLoaded', function () {
            // --- 全局变量 ---
            const loader = document.getElementById('loader');
            const updateBtn = document.getElementById('update-btn');
            const updateStatus = document.getElementById('update-status');
            const mainChart = echarts.init(document.getElementById('main-chart'));
            const signalChart = echarts.init(document.getElementById('signal-chart'));
            let marketData = {};
            let activeIndexId = 'csi300';

          const formulaElement = document.getElementById('formula-display');
            const formulaString = `
            \\text{Signal}(p) =
            \\begin{cases}
                \\frac{0.0295}{1 + e^{-28.45(p - 0.8814)}} & \\text{若 } p > 0.8814 \\\\
                \\frac{-0.0795}{1 + e^{28.45(p - 0.2385)}} & \\text{若 } p < 0.2385 \\\\
                0 & \\text{其他情况}
            \\end{cases}
            `;
            
            try {
                // 步骤1: 使用 renderToString 将公式转换为 HTML 字符串
                const formulaHTML = katex.renderToString(formulaString, {
                    throwOnError: false,
                    displayMode: true
                });
                // 步骤2: 将生成的 HTML 字符串一次性地设置为容器的内容
                formulaElement.innerHTML = formulaHTML;
            } catch (error) {
                console.error("KaTeX formula rendering failed:", error);
                formulaElement.textContent = "公式渲染失败，请刷新页面或检查浏览器控制台。";
            }
            // --- 核心函数 ---

            /**
             * 获取数据并触发初次渲染
             */
            async function fetchDataAndRender() {
                loader.style.display = 'flex';
                try {
                    const response = await fetch('/api/data');
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                    }
                    marketData = await response.json();

                    // 切换到默认标签页并渲染
                    switchTab(activeIndexId);

                } catch (error) {
                    updateStatus.textContent = `数据加载失败: ${error.message}`;
                    console.error('Fetch error:', error);
                } finally {
                    loader.style.display = 'none';
                }
            }

            /**
             * 切换标签页
             * @param {string} indexId - 'csi300' or 'sse50'
             */
            function switchTab(indexId) {
                activeIndexId = indexId;
                // 更新按钮样式
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.index === indexId);
                });
                // 更新整个UI
                updateUI();
            }

            /**
             * 根据当前激活的指数ID，更新所有UI组件
             */
            function updateUI() {
                if (!marketData.dates || marketData.dates.length === 0) return;

                const indexData = {
                    dates: marketData.dates,
                    index_points: marketData[`${activeIndexId}_index`],
                    yield_spread: marketData[`${activeIndexId}_yield_spread`],
                    percentile_history: marketData[`${activeIndexId}_percentile`],
                    percentile_80_line: marketData[`${activeIndexId}_yield_spread_80pct`],
                    percentile_20_line: marketData[`${activeIndexId}_yield_spread_20pct`],
                    latest_percentile: marketData[`latest_${activeIndexId}_percentile`],
                    strategy_signal: marketData[`${activeIndexId}_strategy_signal`],
                    bond_yield_10y: marketData.bond_yield_10y,
                    historical_signals: marketData[`${activeIndexId}_historical_signals`],
                };

                updateStatCard(indexData);
                renderChart(indexData);
                renderSignalChart(indexData);
            }

            /**
             * 更新核心指标卡片
             */
            function updateStatCard(data) {
                const lastIndex = data.dates.length - 1;
                const latestDate = data.dates[lastIndex];

                // --- 计算5年统计数据 ---
                const fiveYearsAgo = new Date(latestDate);
                fiveYearsAgo.setFullYear(fiveYearsAgo.getFullYear() - 5);

                const recentData = [];
                for (let i = 0; i < data.dates.length; i++) {
                    if (new Date(data.dates[i]) >= fiveYearsAgo) {
                        recentData.push(data.yield_spread[i]);
                    }
                }

                const sortedRecentData = [...recentData].sort((a, b) => a - b);
                const stat5y = {
                    min: sortedRecentData[0],
                    max: sortedRecentData[sortedRecentData.length - 1],
                    p20: sortedRecentData[Math.floor(sortedRecentData.length * 0.2)],
                    p80: sortedRecentData[Math.floor(sortedRecentData.length * 0.8)],
                };

                // --- 格式化函数 ---
                const formatValue = (val, digits = 2) => (val !== null && val !== undefined) ? val.toFixed(digits) : '--';
                const formatPercent = (val) => (val !== null && val !== undefined) ? `${(val * 100).toFixed(2)}%` : '--';
                const formatSignal = (val) => {
                    if (val === null || val === undefined) return '--';
                    if (val > 0) return `买入 ${(val * 100).toFixed(2)}%`;
                    if (val < 0) return `卖出 ${(Math.abs(val) * 100).toFixed(2)}%`;
                    return '持有';
                };

                // --- DOM更新 ---
                document.getElementById('latest-date').textContent = latestDate || '--';
                document.getElementById('current-yield-spread').textContent = formatValue(data.yield_spread[lastIndex]);
                document.getElementById('current-percentile').textContent = formatPercent(data.latest_percentile);

                document.getElementById('5y-max').textContent = formatValue(stat5y.max);
                document.getElementById('80-percentile').textContent = formatValue(stat5y.p80);
                document.getElementById('20-percentile').textContent = formatValue(stat5y.p20);
                document.getElementById('5y-min').textContent = formatValue(stat5y.min);

                document.getElementById('current-signal-value').textContent = formatSignal(data.strategy_signal);
            }

            /**
             * 渲染或更新图表
             */
            function renderChart(data) {
                const indexName = activeIndexId === 'csi300' ? '沪深300' : '上证50';
                const option = {
                    title: { text: `${indexName}指数与股债利差`, left: 'center' },
                    tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },
                    legend: { top: 30, data: ['指数点位', '股债利差', '历史分位数', '80%分位线', '20%分位线'] },
                    grid: { left: '50px', right: '50px', bottom: '80px' },
                    xAxis: { type: 'category', data: data.dates },
                    yAxis: [
                        { type: 'value', name: '指数点位', position: 'left', alignTicks: true, axisLine: { show: true, lineStyle: { color: '#5470C6' } } },
                        { type: 'value', name: '利差 (%)', position: 'right', alignTicks: true, axisLine: { show: true, lineStyle: { color: '#91CC75' } }, axisLabel: { formatter: '{value}%' } },
                        { type: 'value', name: '分位数', position: 'right', offset: 60, min: 0, max: 1, axisLine: { show: true, lineStyle: { color: '#FC8452' } }, axisLabel: { formatter: val => `${(val * 100).toFixed(0)}%` } }
                    ],
                    dataZoom: [
                        { type: 'slider', start: 80, end: 100, bottom: 20 },
                        { type: 'inside', start: 80, end: 100 }
                    ],
                    series: [
                        { name: '指数点位', type: 'line', yAxisIndex: 0, data: data.index_points, showSymbol: false, lineStyle: { color: '#5470C6' } },
                        { name: '股债利差', type: 'line', yAxisIndex: 1, data: data.yield_spread, showSymbol: false, lineStyle: { color: '#91CC75', width: 2 } },
                        { name: '历史分位数', type: 'line', yAxisIndex: 2, data: data.percentile_history, showSymbol: false, lineStyle: { color: '#FC8452', width: 2 } },
                        { name: '80%分位线', type: 'line', yAxisIndex: 1, data: data.percentile_80_line, showSymbol: false, lineStyle: { type: 'dashed', color: '#FAC858' } },
                        { name: '20%分位线', type: 'line', yAxisIndex: 1, data: data.percentile_20_line, showSymbol: false, lineStyle: { type: 'dashed', color: '#EE6666' } }
                    ]
                };
                mainChart.setOption(option, true); // `true` to not merge with previous options
            }

            /**
             * 渲染历史买卖信号图表
             */
            function renderSignalChart(data) {
                const indexName = activeIndexId === 'csi300' ? '沪深300' : '上证50';

                // 使用后端计算的历史信号
                const historicalSignals = data.historical_signals || [];

                // 创建买入和卖出信号的散点数据
                const buySignals = [];
                const sellSignals = [];

                for (let i = 0; i < data.dates.length; i++) {
                    const signal = historicalSignals[i];
                    if (signal > 0) {
                        buySignals.push([data.dates[i], signal * 100]); // 转换为百分比
                    } else if (signal < 0) {
                        sellSignals.push([data.dates[i], Math.abs(signal) * 100]); // 转换为百分比
                    }
                }

                const option = {
                    title: { text: `${indexName}历史买卖信号`, left: 'center' },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            let result = params[0].axisValue + '<br/>';
                            params.forEach(param => {
                                if (param.seriesName === '买入信号') {
                                    result += `${param.seriesName}: +${param.value[1].toFixed(2)}%<br/>`;
                                } else if (param.seriesName === '卖出信号') {
                                    result += `${param.seriesName}: -${param.value[1].toFixed(2)}%<br/>`;
                                } else {
                                    result += `${param.seriesName}: ${param.value.toFixed(4)}<br/>`;
                                }
                            });
                            return result;
                        }
                    },
                    legend: { top: 30, data: ['历史分位数', '买入信号', '卖出信号'] },
                    grid: { left: '50px', right: '50px', bottom: '80px' },
                    xAxis: { type: 'category', data: data.dates },
                    yAxis: [
                        {
                            type: 'value',
                            name: '分位数',
                            position: 'left',
                            min: 0,
                            max: 1,
                            axisLabel: { formatter: val => `${(val * 100).toFixed(0)}%` }
                        },
                        {
                            type: 'value',
                            name: '信号强度 (%)',
                            position: 'right',
                            axisLabel: { formatter: '{value}%' }
                        }
                    ],
                    dataZoom: [
                        { type: 'slider', start: 80, end: 100, bottom: 20 },
                        { type: 'inside', start: 80, end: 100 }
                    ],
                    series: [
                        {
                            name: '历史分位数',
                            type: 'line',
                            yAxisIndex: 0,
                            data: data.percentile_history,
                            showSymbol: false,
                            lineStyle: { color: '#FC8452', width: 1 }
                        },
                        {
                            name: '买入信号',
                            type: 'scatter',
                            yAxisIndex: 1,
                            data: buySignals,
                            symbolSize: 8,
                            itemStyle: { color: '#28a745' }
                        },
                        {
                            name: '卖出信号',
                            type: 'scatter',
                            yAxisIndex: 1,
                            data: sellSignals,
                            symbolSize: 8,
                            itemStyle: { color: '#dc3545' }
                        }
                    ]
                };
                signalChart.setOption(option, true);
            }

            // --- 事件绑定 ---
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', () => switchTab(btn.dataset.index));
            });

            updateBtn.addEventListener('click', async () => {
                updateBtn.disabled = true;
                updateStatus.textContent = '正在更新数据，请耐心等待...';
                loader.style.display = 'flex';

                try {
                    const response = await fetch('/api/update', { method: 'POST' });
                    const result = await response.json();
                    if (result.status === 'success') {
                        updateStatus.textContent = '更新成功！正在刷新...';
                        await fetchDataAndRender(); // 更新成功后重新获取并渲染
                        updateStatus.textContent = '图表已刷新。';
                    } else {
                        throw new Error(result.message || '未知错误');
                    }
                } catch (error) {
                    updateStatus.textContent = `更新失败: ${error.message}`;
                    console.error('Update error:', error);
                } finally {
                    updateBtn.disabled = false;
                    loader.style.display = 'none';
                    setTimeout(() => { updateStatus.textContent = ''; }, 3000);
                }
            });

            window.addEventListener('resize', () => {
                mainChart.resize();
                signalChart.resize();
            });

            // --- 初始加载 ---
            fetchDataAndRender();
        });
    </script>
</body>

</html>