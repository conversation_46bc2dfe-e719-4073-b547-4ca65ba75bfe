<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股债收益差监控</title>
    <!-- 引入 ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .main-container {
            width: 100%;
            max-width: 1400px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #212529;
        }
        .update-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        #update-btn {
            padding: 8px 16px;
            font-size: 14px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        #update-btn:hover:not(:disabled) {
            background-color: #0056b3;
        }
        #update-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #update-status {
            font-size: 14px;
            color: #6c757d;
        }
        .stats-container {
            display: grid;
            /* 调整网格布局以适应三个卡片 */
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            width: 100%;
        }
        .stat-card {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }
        .stat-card h2 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            color: #495057;
            border-bottom: 1px solid #f1f3f5;
            padding-bottom: 10px;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            padding: 5px 0;
        }
        .stat-item .label {
            color: #6c757d;
        }
        .stat-item .value {
            font-weight: 600;
            color: #212529;
        }
        .chart-container {
            width: 100%;
            height: 500px;
            background-color: #ffffff;
            border-radius: 12px;
            padding: 20px;
            box-sizing: border-box;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }
        .loader-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            flex-direction: column;
            gap: 20px;
        }
        .spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>

    <!-- 加载动画 -->
    <div id="loader" class="loader-overlay">
        <div class="spinner"></div>
        <p>正在加载数据，请稍候...</p>
    </div>

    <div class="main-container">
        <div class="header">
            <h1>股债收益差监控面板</h1>
            <div class="update-container">
                <span id="update-status"></span>
                <button id="update-btn">更新数据</button>
            </div>
        </div>

        <!-- 指标展示 -->
        <div class="stats-container">
            <div class="stat-card">
                <h2>最新指标 (沪深300)</h2>
                <div class="stat-item">
                    <span class="label">最新日期:</span>
                    <span class="value" id="latest-date-csi300">--</span>
                </div>
                <div class="stat-item">
                    <span class="label">指数点位:</span>
                    <span class="value" id="latest-csi300-index">--</span>
                </div>
                <div class="stat-item">
                    <span class="label">股债收益差 (%):</span>
                    <span class="value" id="latest-csi300-yield-spread">--</span>
                </div>
            </div>
            <div class="stat-card">
                <h2>最新指标 (上证50)</h2>
                <div class="stat-item">
                    <span class="label">最新日期:</span>
                    <span class="value" id="latest-date-sse50">--</span>
                </div>
                <div class="stat-item">
                    <span class="label">指数点位:</span>
                    <span class="value" id="latest-sse50-index">--</span>
                </div>
                <div class="stat-item">
                    <span class="label">股债收益差 (%):</span>
                    <span class="value" id="latest-sse50-yield-spread">--</span>
                </div>
            </div>
            <!-- 新增的关键利率卡片 -->
            <div class="stat-card">
                <h2>关键利率</h2>
                <div class="stat-item">
                    <span class="label">最新日期:</span>
                    <span class="value" id="latest-date-bond">--</span>
                </div>
                <div class="stat-item">
                    <span class="label">十年期国债收益率 (%):</span>
                    <span class="value" id="latest-bond-yield">--</span>
                </div>
            </div>
        </div>

        <!-- ECharts 图表容器 -->
        <div id="csi300-chart" class="chart-container"></div>
        <div id="sse50-chart" class="chart-container"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 获取DOM元素
            const loader = document.getElementById('loader');
            const updateBtn = document.getElementById('update-btn');
            const updateStatus = document.getElementById('update-status');

            // 初始化ECharts实例
            const csi300Chart = echarts.init(document.getElementById('csi300-chart'));
            const sse50Chart = echarts.init(document.getElementById('sse50-chart'));

            // 通用图表配置
            const getBaseChartOption = (title) => ({
                title: {
                    text: title,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                legend: {
                    top: 30,
                    // 增加图例
                    data: ['指数点位', '股债收益差', '十年期国债收益率', '80%分位线', '20%分位线']
                },
                grid: {
                    left: '50px',
                    right: '50px',
                    bottom: '80px' // 为dataZoom留出空间
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisPointer: {
                        label: {
                            formatter: function (params) {
                                return '日期: ' + params.value;
                            }
                        }
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '指数点位',
                        position: 'left',
                        alignTicks: true,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#5470C6'
                            }
                        },
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                    {
                        type: 'value',
                        name: '收益率差 (%)',
                        position: 'right',
                        alignTicks: true,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#91CC75'
                            }
                        },
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    }
                ],
                dataZoom: [
                    {
                        type: 'slider',
                        start: 80,
                        end: 100,
                        bottom: 20
                    },
                    {
                        type: 'inside',
                        start: 80,
                        end: 100
                    }
                ],
                series: []
            });

            // 更新顶部指标卡片
            function updateStatCards(data) {
                const lastIndex = data.dates.length - 1;
                
                // 格式化数字，保留两位小数
                const formatValue = (val) => (val !== null && val !== undefined) ? val.toFixed(4) : '--';

                // 沪深300
                document.getElementById('latest-date-csi300').textContent = data.dates[lastIndex] || '--';
                document.getElementById('latest-csi300-index').textContent = formatValue(data.csi300_index[lastIndex]);
                document.getElementById('latest-csi300-yield-spread').textContent = formatValue(data.csi300_yield_spread[lastIndex]);

                // 上证50
                document.getElementById('latest-date-sse50').textContent = data.dates[lastIndex] || '--';
                document.getElementById('latest-sse50-index').textContent = formatValue(data.sse50_index[lastIndex]);
                document.getElementById('latest-sse50-yield-spread').textContent = formatValue(data.sse50_yield_spread[lastIndex]);

                // 十年期国债
                document.getElementById('latest-date-bond').textContent = data.dates[lastIndex] || '--';
                document.getElementById('latest-bond-yield').textContent = formatValue(data.bond_yield_10y[lastIndex]);
            }

            // 渲染图表
            function renderCharts(data) {
                // 沪深300 图表配置
                const csi300Option = getBaseChartOption('沪深300指数与股债收益差');
                csi300Option.xAxis.data = data.dates;
                csi300Option.series = [
                    {
                        name: '指数点位',
                        type: 'line',
                        yAxisIndex: 0,
                        data: data.csi300_index,
                        showSymbol: false,
                        lineStyle: { color: '#5470C6' }
                    },
                    {
                        name: '股债收益差',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.csi300_yield_spread,
                        showSymbol: false,
                        lineStyle: { color: '#91CC75', width: 2 }
                    },
                    // 新增国债收益率系列
                    {
                        name: '十年期国债收益率',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.bond_yield_10y,
                        showSymbol: false,
                        lineStyle: { color: '#73C0DE' }
                    },
                    {
                        name: '80%分位线',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.csi300_yield_spread_80pct,
                        showSymbol: false,
                        lineStyle: { type: 'dashed', color: '#FAC858' }
                    },
                    {
                        name: '20%分位线',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.csi300_yield_spread_20pct,
                        showSymbol: false,
                        lineStyle: { type: 'dashed', color: '#EE6666' }
                    }
                ];
                csi300Chart.setOption(csi300Option);

                // 上证50 图表配置
                const sse50Option = getBaseChartOption('上证50指数与股债收益差');
                sse50Option.xAxis.data = data.dates;
                sse50Option.series = [
                    {
                        name: '指数点位',
                        type: 'line',
                        yAxisIndex: 0,
                        data: data.sse50_index,
                        showSymbol: false,
                        lineStyle: { color: '#5470C6' }
                    },
                    {
                        name: '股债收益差',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.sse50_yield_spread,
                        showSymbol: false,
                        lineStyle: { color: '#91CC75', width: 2 }
                    },
                    // 新增国债收益率系列
                    {
                        name: '十年期国债收益率',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.bond_yield_10y,
                        showSymbol: false,
                        lineStyle: { color: '#73C0DE' }
                    },
                    {
                        name: '80%分位线',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.sse50_yield_spread_80pct,
                        showSymbol: false,
                        lineStyle: { type: 'dashed', color: '#FAC858' }
                    },
                    {
                        name: '20%分位线',
                        type: 'line',
                        yAxisIndex: 1,
                        data: data.sse50_yield_spread_20pct,
                        showSymbol: false,
                        lineStyle: { type: 'dashed', color: '#EE6666' }
                    }
                ];
                sse50Chart.setOption(sse50Option);
            }

            // 获取数据并渲染
            async function fetchDataAndRender() {
                loader.style.display = 'flex';
                try {
                    const response = await fetch('/api/data');
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();
                    if (!data.bond_yield_10y) {
                         throw new Error("后端返回的数据中缺少 'bond_yield_10y' 字段。");
                    }
                    updateStatCards(data);
                    renderCharts(data);
                } catch (error) {
                    updateStatus.textContent = `数据加载失败: ${error.message}`;
                    console.error('Fetch error:', error);
                } finally {
                    loader.style.display = 'none';
                }
            }

            // 绑定更新按钮事件
            updateBtn.addEventListener('click', async () => {
                updateBtn.disabled = true;
                updateStatus.textContent = '正在更新数据，请耐心等待...';
                loader.style.display = 'flex';

                try {
                    const response = await fetch('/api/update', { method: 'POST' });
                    const result = await response.json();
                    if (result.status === 'success') {
                        updateStatus.textContent = '更新成功！正在刷新图表...';
                        await fetchDataAndRender(); // 更新成功后重新获取数据
                        updateStatus.textContent = '图表已刷新。';
                    } else {
                        throw new Error(result.message || '未知错误');
                    }
                } catch (error) {
                    updateStatus.textContent = `更新失败: ${error.message}`;
                    console.error('Update error:', error);
                } finally {
                    updateBtn.disabled = false;
                    loader.style.display = 'none';
                    // 3秒后清除状态消息
                    setTimeout(() => { updateStatus.textContent = ''; }, 3000);
                }
            });

            // 联动图表
            echarts.connect([csi300Chart, sse50Chart]);

            // 监听窗口大小变化，重绘图表
            window.addEventListener('resize', () => {
                csi300Chart.resize();
                sse50Chart.resize();
            });

            // 初始加载数据
            fetchDataAndRender();
        });
    </script>
</body>
</html>
