import traceback
import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import sys
import io
import numpy as np
from flask import Flask, jsonify, render_template
from tqdm import tqdm

# 设置基础路径，指向项目根目录
BASE_DIR = Path(__file__).resolve().parent

# 定义各个数据文件的存储路径
DATA_DIR = BASE_DIR / 'Data'
FILES = {
    'bond_yield': DATA_DIR / 'bond_yield_data.csv',
    'csi300_index': DATA_DIR / 'csi300_index_data.csv',
    'csi300_pe': DATA_DIR / 'csi300_pe_data.csv',
    'sse50_index': DATA_DIR / 'sse50_index_data.csv',
    'sse50_pe': DATA_DIR / 'sse50_pe_data.csv'
}

# 初始化 Flask 应用，模板文件和静态文件都在项目根目录
app = Flask(__name__, template_folder=BASE_DIR, static_folder=BASE_DIR)

# 确保数据目录存在
DATA_DIR.mkdir(parents=True, exist_ok=True)

# 修复 PyInstaller 环境下 sys.stdout 和 sys.stderr 可能为 None 的情况
if sys.stdout is None:
    sys.stdout = io.StringIO()
if sys.stderr is None:
    sys.stderr = sys.stdout

# --- 数据爬取与处理函数 ---

def fetch_data_by_year(start_date, end_date):
    """
    根据给定的开始和结束日期，按年爬取一次数据
    """
    print(f"正在爬取 {start_date} 至 {end_date} 的数据...")
    
    start_str = start_date.strftime('%Y%m%d') if isinstance(start_date, datetime) else start_date
    end_str = end_date.strftime('%Y%m%d') if isinstance(end_date, datetime) else end_date
    
    fetched_data = {}
    with tqdm(total=5, file=sys.stdout, desc="数据获取进度") as pbar:
        try:
            fetched_data['bond_yield'] = ak.bond_china_yield(start_date=start_str, end_date=end_str)
        except Exception as e:
            print(f"获取国债收益率数据失败: {e}")
            fetched_data['bond_yield'] = pd.DataFrame()
        pbar.update(1)
        
        try:
            fetched_data['csi300_index'] = ak.index_zh_a_hist(symbol="000300", period="daily", start_date=start_str, end_date=end_str)
        except Exception as e:
            print(f"获取沪深300指数数据失败: {e}")
            fetched_data['csi300_index'] = pd.DataFrame()
        pbar.update(1)
        
        try:
            csi300_pe_df = ak.stock_index_pe_lg(symbol="沪深300")
            csi300_pe_df['日期'] = pd.to_datetime(csi300_pe_df['日期'])
            fetched_data['csi300_pe'] = csi300_pe_df[(csi300_pe_df['日期'] >= pd.to_datetime(start_str)) & (csi300_pe_df['日期'] <= pd.to_datetime(end_str))]
        except Exception as e:
            print(f"获取沪深300市盈率数据失败: {e}")
            fetched_data['csi300_pe'] = pd.DataFrame()
        pbar.update(1)
        
        try:
            fetched_data['sse50_index'] = ak.index_zh_a_hist(symbol="000016", period="daily", start_date=start_str, end_date=end_str)
        except Exception as e:
            print(f"获取上证50指数数据失败: {e}")
            fetched_data['sse50_index'] = pd.DataFrame()
        pbar.update(1)
        
        try:
            sse50_pe_df = ak.stock_index_pe_lg(symbol="上证50")
            sse50_pe_df['日期'] = pd.to_datetime(sse50_pe_df['日期'])
            fetched_data['sse50_pe'] = sse50_pe_df[(sse50_pe_df['日期'] >= pd.to_datetime(start_str)) & (sse50_pe_df['日期'] <= pd.to_datetime(end_str))]
        except Exception as e:
            print(f"获取上证50市盈率数据失败: {e}")
            fetched_data['sse50_pe'] = pd.DataFrame()
        pbar.update(1)
    
    return fetched_data

def update_data(start_date, end_date):
    """
    更新本地数据到最新
    """
    today = pd.Timestamp(end_date).normalize()
    
    for key, file_path in FILES.items():
        latest_date_in_file = None
        if file_path.exists():
            try:
                df = pd.read_csv(file_path)
                if '日期' in df.columns and not df.empty:
                    df['日期'] = pd.to_datetime(df['日期'])
                    latest_date_in_file = df['日期'].max().normalize()
            except Exception as e:
                print(f"读取文件 {file_path} 失败: {e}")
                df = pd.DataFrame()
        else:
            df = pd.DataFrame()

        fetch_start_date = latest_date_in_file + timedelta(days=1) if pd.notna(latest_date_in_file) else pd.Timestamp(start_date)

        if fetch_start_date > today:
            print(f"{key} 数据已是最新，无需更新。")
            continue

        print(f"正在更新 {key}，从 {fetch_start_date.strftime('%Y-%m-%d')} 到 {today.strftime('%Y-%m-%d')}...")
        new_data_dict = fetch_data_by_year(fetch_start_date, today)
        new_df = new_data_dict.get(key)

        if new_df is None or new_df.empty:
            print(f"未能获取到 {key} 的新数据，跳过。")
            continue

        if '日期' not in new_df.columns:
            print(f"警告：{key} 的新数据缺少 '日期' 列，跳过。")
            continue
        
        new_df['日期'] = pd.to_datetime(new_df['日期'])
        
        # 合并数据
        combined_df = pd.concat([df, new_df], ignore_index=True)
        # 去重并排序
        combined_df = combined_df.drop_duplicates(subset=['日期']).sort_values('日期').reset_index(drop=True)
        
        combined_df.to_csv(file_path, index=False, encoding='utf-8-sig')
        print(f"{key} 数据已保存到: {file_path}")

    print("所有数据更新检查完成。")

def calculate_percentile_for_date(df, date_str, spread_column, window_days=5*252):
    """
    计算指定日期的股债利差在前五年的分位数

    Args:
        df: 数据DataFrame
        date_str: 日期字符串 (YYYY-MM-DD)
        spread_column: 股债利差列名
        window_days: 回看窗口天数，默认5年

    Returns:
        percentile: 分位数 (0-1)
    """
    try:
        target_date = pd.to_datetime(date_str)

        # 找到目标日期在数据中的位置
        df_filtered = df[df['date'] <= target_date].copy()

        if df_filtered.empty:
            return None

        # 获取目标日期的股债利差值
        target_row = df_filtered[df_filtered['date'] == target_date]
        if target_row.empty:
            # 如果没有精确匹配，使用最近的日期
            target_row = df_filtered.iloc[[-1]]

        target_value = target_row[spread_column].iloc[0]

        if pd.isna(target_value):
            return None

        # 获取前五年的数据
        start_date = target_date - timedelta(days=window_days)
        historical_data = df_filtered[
            (df_filtered['date'] >= start_date) &
            (df_filtered['date'] <= target_date)
        ][spread_column].dropna()

        if len(historical_data) < 10:  # 至少需要10个数据点
            return None

        # 计算分位数
        percentile = (historical_data < target_value).sum() / len(historical_data)
        return percentile

    except Exception as e:
        print(f"计算分位数时出错: {e}")
        return None

def sigmoid_strategy(percentile, params=None):
    """
    Sigmoid策略函数

    Args:
        percentile: 股债利差分位数 (0-1)
        params: 参数字典

    Returns:
        action_ratio: 正数表示买入比例，负数表示卖出比例
    """
    if params is None:
        params = {}

    sell_center = params.get('sell_center', 0.2385)
    buy_center = params.get('buy_center', 0.8814)
    sell_ratio = params.get('sell_ratio', 0.0795)
    buy_ratio = params.get('buy_ratio', 0.0295)
    steepness = params.get('steepness', 28.4524)

    # Sigmoid函数用于卖出（低分位数时）
    sell_signal = 1 / (1 + np.exp(steepness * (percentile - sell_center)))

    # Sigmoid函数用于买入（高分位数时）
    buy_signal = 1 / (1 + np.exp(-steepness * (percentile - buy_center)))

    # 计算最终操作比例
    if sell_signal > 0.5:
        return -sell_ratio * sell_signal
    elif buy_signal > 0.5:
        return buy_ratio * buy_signal
    else:
        return 0

def process_data():
    """
    读取并处理数据，计算收益率差及历史分位值
    """
    data_dict = {}
    for key, file_path in FILES.items():
        if file_path.exists():
            data_dict[key] = pd.read_csv(file_path)
        else:
            print(f"错误: 数据文件 {file_path} 不存在。请先运行更新。")
            return None

    if any(df.empty for df in data_dict.values()):
        print("错误: 一个或多个数据文件为空。")
        return None

    csi300_index = data_dict['csi300_index'][['日期', '收盘']].rename(columns={'收盘': 'csi300_index'})
    csi300_index['日期'] = pd.to_datetime(csi300_index['日期'])

    sse50_index = data_dict['sse50_index'][['日期', '收盘']].rename(columns={'收盘': 'sse50_index'})
    sse50_index['日期'] = pd.to_datetime(sse50_index['日期'])

    bond_yield_10y = data_dict['bond_yield'][
        data_dict['bond_yield']['曲线名称'] == '中债国债收益率曲线'
    ][['日期', '10年']].rename(columns={'10年': 'bond_yield_10y'})
    bond_yield_10y['日期'] = pd.to_datetime(bond_yield_10y['日期'])

    csi300_pe = data_dict['csi300_pe'][['日期', '滚动市盈率']].rename(columns={'滚动市盈率': 'csi300_pe'})
    csi300_pe['日期'] = pd.to_datetime(csi300_pe['日期'])

    sse50_pe = data_dict['sse50_pe'][['日期', '滚动市盈率']].rename(columns={'滚动市盈率': 'sse50_pe'})
    sse50_pe['日期'] = pd.to_datetime(sse50_pe['日期'])

    df = pd.merge(bond_yield_10y, csi300_index, on='日期', how='outer')
    df = pd.merge(df, csi300_pe, on='日期', how='outer')
    df = pd.merge(df, sse50_index, on='日期', how='outer')
    df = pd.merge(df, sse50_pe, on='日期', how='outer')

    df = df.rename(columns={'日期': 'date'}).sort_values('date').reset_index(drop=True)
    df[['csi300_pe', 'sse50_pe', 'bond_yield_10y', 'csi300_index', 'sse50_index']] = df[['csi300_pe', 'sse50_pe', 'bond_yield_10y', 'csi300_index', 'sse50_index']].ffill()

    df['csi300_yield_spread'] = 100 / df['csi300_pe'] - df['bond_yield_10y']
    df['sse50_yield_spread'] = 100 / df['sse50_pe'] - df['bond_yield_10y']

    df['csi300_yield_spread_20pct'] = df['csi300_yield_spread'].rolling(window=5*252, min_periods=252).quantile(0.2)
    df['csi300_yield_spread_80pct'] = df['csi300_yield_spread'].rolling(window=5*252, min_periods=252).quantile(0.8)
    df['sse50_yield_spread_20pct'] = df['sse50_yield_spread'].rolling(window=5*252, min_periods=252).quantile(0.2)
    df['sse50_yield_spread_80pct'] = df['sse50_yield_spread'].rolling(window=5*252, min_periods=252).quantile(0.8)

    # 计算每日的分位数（优化版本）
    df['csi300_percentile'] = np.nan
    df['sse50_percentile'] = np.nan

    # 使用滚动窗口计算分位数，更高效
    window_size = 5 * 252  # 5年

    for i in range(252, len(df)):  # 至少需要一年的数据
        start_idx = max(0, i - window_size + 1)
        end_idx = i + 1

        # 获取历史数据窗口
        csi300_window = df.iloc[start_idx:end_idx]['csi300_yield_spread'].dropna()
        sse50_window = df.iloc[start_idx:end_idx]['sse50_yield_spread'].dropna()

        # 获取当前值
        current_csi300 = df.iloc[i]['csi300_yield_spread']
        current_sse50 = df.iloc[i]['sse50_yield_spread']

        # 计算分位数
        if not pd.isna(current_csi300) and len(csi300_window) > 10:
            df.loc[i, 'csi300_percentile'] = (csi300_window < current_csi300).sum() / len(csi300_window)

        if not pd.isna(current_sse50) and len(sse50_window) > 10:
            df.loc[i, 'sse50_percentile'] = (sse50_window < current_sse50).sum() / len(sse50_window)

    return df

# --- Flask 路由和 API 定义 ---

@app.route('/')
def index():
    """
    主页路由，渲染前端 HTML 页面
    """
    return render_template('index.html')

@app.route('/api/data')
def get_data_api():
    """
    获取处理后的数据和统计信息的 API
    """
    try:
        df = process_data()
        if df is None:
            return jsonify({"error": "数据处理失败，请先更新数据"}), 500

        # 修复 NaN/NaT 导致 JSON 解析错误的问题
        df = df.replace({np.nan: None, pd.NaT: None})

        # 计算最新的策略信号
        latest_csi300_percentile = df['csi300_percentile'].iloc[-1] if not pd.isna(df['csi300_percentile'].iloc[-1]) else None
        latest_sse50_percentile = df['sse50_percentile'].iloc[-1] if not pd.isna(df['sse50_percentile'].iloc[-1]) else None

        csi300_strategy_signal = None
        sse50_strategy_signal = None

        if latest_csi300_percentile is not None:
            csi300_strategy_signal = sigmoid_strategy(latest_csi300_percentile)

        if latest_sse50_percentile is not None:
            sse50_strategy_signal = sigmoid_strategy(latest_sse50_percentile)

        # 计算历史策略信号
        df['csi300_historical_signals'] = df['csi300_percentile'].apply(
            lambda x: sigmoid_strategy(x) if not pd.isna(x) else None
        )
        df['sse50_historical_signals'] = df['sse50_percentile'].apply(
            lambda x: sigmoid_strategy(x) if not pd.isna(x) else None
        )

        data_to_send = {
            'dates': df['date'].dt.strftime('%Y-%m-%d').tolist(),
            'csi300_index': df['csi300_index'].tolist(),
            'sse50_index': df['sse50_index'].tolist(),
            'csi300_yield_spread': df['csi300_yield_spread'].tolist(),
            'sse50_yield_spread': df['sse50_yield_spread'].tolist(),
            # *** 新增返回十年期国债收益率 ***
            'bond_yield_10y': df['bond_yield_10y'].tolist(),
            'csi300_yield_spread_20pct': df['csi300_yield_spread_20pct'].tolist(),
            'csi300_yield_spread_80pct': df['csi300_yield_spread_80pct'].tolist(),
            'sse50_yield_spread_20pct': df['sse50_yield_spread_20pct'].tolist(),
            'sse50_yield_spread_80pct': df['sse50_yield_spread_80pct'].tolist(),
            # *** 新增分位数和策略信号 ***
            'csi300_percentile': df['csi300_percentile'].tolist(),
            'sse50_percentile': df['sse50_percentile'].tolist(),
            'latest_csi300_percentile': latest_csi300_percentile,
            'latest_sse50_percentile': latest_sse50_percentile,
            'csi300_strategy_signal': csi300_strategy_signal,
            'sse50_strategy_signal': sse50_strategy_signal,
            # *** 新增历史策略信号 ***
            'csi300_historical_signals': df['csi300_historical_signals'].tolist(),
            'sse50_historical_signals': df['sse50_historical_signals'].tolist(),
        }
        return jsonify(data_to_send)
    except Exception as e:
        print(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@app.route('/api/update', methods=['POST'])
def update_data_api():
    """
    更新数据的 API
    """
    try:
        ten_years_ago = datetime.today() - timedelta(days=365 * 10)
        yesterday = datetime.today() - timedelta(days=1)
        update_data(ten_years_ago, yesterday)
        return jsonify({"status": "success", "message": "数据已更新"})
    except Exception as e:
        print(traceback.format_exc())
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    # 启动时执行一次数据更新检查
    try:
        print("启动时检查数据更新...")
        ten_years_ago = datetime.today() - timedelta(days=365 * 10)
        yesterday = datetime.today() - timedelta(days=1)
        update_data(ten_years_ago, yesterday)
    except Exception as e:
        print(f"初始化数据更新失败: {e}")
        print(traceback.format_exc())

    app.run(debug=True, host='0.0.0.0')
